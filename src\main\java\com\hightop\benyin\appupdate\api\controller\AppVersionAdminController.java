package com.hightop.benyin.appupdate.api.controller;

import com.hightop.benyin.appupdate.api.dto.AppVersionPublishDto;
import com.hightop.benyin.appupdate.api.dto.query.AppVersionPageQuery;
import com.hightop.benyin.appupdate.api.vo.AppVersionVo;
import com.hightop.benyin.appupdate.application.service.AppVersionService;
import com.hightop.fario.base.web.Operation;
import com.hightop.fario.base.web.RestResponse;
import com.hightop.fario.common.mybatis.DataGrid;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 应用版本管理控制器
 * <AUTHOR>
 * @date 2025-01-29
 */
@RestController
@RequestMapping("/admin/app-version")
@Api(tags = "应用版本管理")
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class AppVersionAdminController {
    
    AppVersionService appVersionService;
    
    /**
     * 版本列表分页查询
     * @param pageQuery 查询条件
     * @return 分页结果
     */
    @PostMapping("/page")
    @ApiOperation("版本列表分页查询")
    public RestResponse<DataGrid<AppVersionVo>> page(@RequestBody AppVersionPageQuery pageQuery) {
        return RestResponse.ok(appVersionService.page(pageQuery));
    }
    
    /**
     * 发布新版本
     * @param publishDto 发布信息
     * @return 版本ID
     */
    @PostMapping("/publish")
    @ApiOperation("发布新版本")
    public RestResponse<Long> publish(@Validated @RequestBody AppVersionPublishDto publishDto) {
        return RestResponse.ok(appVersionService.publishVersion(publishDto));
    }
    
    /**
     * 更新版本信息
     * @param id 版本ID
     * @param versionName 版本名称
     * @param updateLog 更新说明
     * @param isForce 是否强制更新
     * @param isActive 是否启用
     * @return 操作结果
     */
    @PutMapping("/{id}")
    @ApiOperation("更新版本信息")
    public RestResponse<Void> update(@PathVariable Long id,
                                   @RequestParam String versionName,
                                   @RequestParam(required = false) String updateLog,
                                   @RequestParam(required = false) Boolean isForce,
                                   @RequestParam(required = false) Boolean isActive) {
        return Operation.UPDATE.response(
            appVersionService.updateVersion(id, versionName, updateLog, isForce, isActive));
    }
    
    /**
     * 设置/取消强制更新
     * @param id 版本ID
     * @param force 是否强制
     * @return 操作结果
     */
    @PostMapping("/{id}/force")
    @ApiOperation("设置/取消强制更新")
    public RestResponse<Void> toggleForce(@PathVariable Long id, 
                                        @RequestParam Boolean force) {
        return Operation.UPDATE.response(appVersionService.toggleForce(id, force));
    }
    
    /**
     * 删除版本
     * @param id 版本ID
     * @return 操作结果
     */
    @DeleteMapping("/{id}")
    @ApiOperation("删除版本")
    public RestResponse<Void> delete(@PathVariable Long id) {
        return Operation.DELETE.response(appVersionService.deleteVersion(id));
    }
    
    /**
     * 紧急操作
     * @param action 操作类型
     * @param params 参数
     * @return 操作结果
     */
    @PostMapping("/emergency/{action}")
    @ApiOperation("紧急操作")
    public RestResponse<Void> emergency(@PathVariable String action, 
                                      @RequestBody(required = false) Map<String, Object> params) {
        return Operation.UPDATE.response(appVersionService.emergencyAction(action, params));
    }
}
