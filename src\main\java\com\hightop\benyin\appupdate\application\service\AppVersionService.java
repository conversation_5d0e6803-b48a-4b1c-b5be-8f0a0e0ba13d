package com.hightop.benyin.appupdate.application.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hightop.benyin.appupdate.api.dto.AppVersionPublishDto;
import com.hightop.benyin.appupdate.api.dto.query.AppVersionPageQuery;
import com.hightop.benyin.appupdate.api.vo.AppVersionVo;
import com.hightop.benyin.appupdate.domain.service.AppVersionDomainService;
import com.hightop.benyin.appupdate.infrastructure.entity.AppVersion;
import com.hightop.benyin.share.application.service.CosService;
import com.hightop.benyin.share.application.vo.CosBucket;
import com.hightop.benyin.share.domain.service.SequenceDomainService;
import com.hightop.benyin.share.infrastructure.restful.tencent.CosProperties;
import com.hightop.fario.base.util.StringUtils;
import com.hightop.fario.base.web.Operation;
import com.hightop.fario.common.mybatis.DataGrid;
import com.hightop.fario.common.mybatis.util.PageHelper;
import com.hightop.magina.casual.session.ApplicationSessions;
import com.hightop.magina.core.exception.MaginaException;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 应用版本管理服务
 * <AUTHOR>
 * @date 2025-01-29
 */
@Service
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Transactional
@Slf4j
public class AppVersionService {
    
    AppVersionDomainService appVersionDomainService;
    CosService cosService;
    SequenceDomainService sequenceDomainService;
    CosProperties cosProperties;
    
    /**
     * 分页查询版本列表
     * @param pageQuery 查询条件
     * @return 分页结果
     */
    public DataGrid<AppVersionVo> page(AppVersionPageQuery pageQuery) {
        LambdaQueryWrapper<AppVersion> wrapper = Wrappers.<AppVersion>lambdaQuery()
                .like(StringUtils.isNotEmpty(pageQuery.getVersionName()), 
                      AppVersion::getVersionName, pageQuery.getVersionName())
                .eq(pageQuery.getIsActive() != null, 
                    AppVersion::getIsActive, pageQuery.getIsActive())
                .eq(pageQuery.getIsForce() != null, 
                    AppVersion::getIsForce, pageQuery.getIsForce())
                .orderByDesc(AppVersion::getVersionCode);
        
        return PageHelper.startPage(pageQuery, p -> {
            List<AppVersion> list = appVersionDomainService.list(wrapper);
            return list.stream().map(this::convertToVo).collect(Collectors.toList());
        });
    }
    
    /**
     * 发布新版本
     * @param publishDto 发布信息
     * @return 版本ID
     */
    public Long publishVersion(AppVersionPublishDto publishDto) {
        // 验证版本号唯一性
        validateVersionUnique(publishDto.getVersionName(), publishDto.getVersionCode(), null);

        // 创建版本记录（使用前端已上传的文件信息）
        AppVersion version = new AppVersion()
                .setVersionName(publishDto.getVersionName())
                .setVersionCode(publishDto.getVersionCode())
                .setApkFileName(publishDto.getApkFileName())
                .setCosKey(publishDto.getCosKey())
                .setCosUrl(publishDto.getCosUrl())
                .setFileSize(publishDto.getFileSize())
                .setFileMd5(publishDto.getFileMd5())
                .setUpdateLog(publishDto.getUpdateLog())
                .setIsForce(publishDto.getIsForce())
                .setAdminForce(false)
                .setIsActive(publishDto.getIsActive())
                .setDownloadCount(0)
                .setCreatedBy(ApplicationSessions.id());

        appVersionDomainService.save(version);

        return version.getId();
    }
    
    /**
     * 更新版本信息
     * @param id 版本ID
     * @param versionName 版本名称
     * @param updateLog 更新说明
     * @param isForce 是否强制更新
     * @param isActive 是否启用
     * @return 是否成功
     */
    public boolean updateVersion(Long id, String versionName, String updateLog, 
                               Boolean isForce, Boolean isActive) {
        AppVersion version = appVersionDomainService.getById(id);
        if (version == null) {
            throw new MaginaException("版本不存在");
        }
        
        // 如果修改了版本名称，需要验证唯一性
        if (!version.getVersionName().equals(versionName)) {
            validateVersionUnique(versionName, null, id);
        }
        
        return appVersionDomainService.lambdaUpdate()
                .set(AppVersion::getVersionName, versionName)
                .set(AppVersion::getUpdateLog, updateLog)
                .set(AppVersion::getIsForce, isForce)
                .set(AppVersion::getIsActive, isActive)
                .set(AppVersion::getUpdatedBy, ApplicationSessions.id())
                .eq(AppVersion::getId, id)
                .update();
    }
    
    /**
     * 设置/取消强制更新
     * @param id 版本ID
     * @param force 是否强制
     * @return 是否成功
     */
    public boolean toggleForce(Long id, Boolean force) {
        return appVersionDomainService.setForceUpdate(id, force);
    }
    
    /**
     * 删除版本
     * @param id 版本ID
     * @return 是否成功
     */
    public boolean deleteVersion(Long id) {
        return appVersionDomainService.removeById(id);
    }
    
    /**
     * 紧急操作处理
     * @param action 操作类型
     * @param params 参数
     * @return 是否成功
     */
    public boolean emergencyAction(String action, Map<String, Object> params) {
        switch (action) {
            case "rollback":
                return handleRollback(params);
            case "pause":
                return handlePauseUpdates();
            case "resume":
                return handleResumeUpdates();
            default:
                throw new MaginaException("不支持的紧急操作: " + action);
        }
    }
    
    /**
     * 处理版本回退
     * @param params 参数
     * @return 是否成功
     */
    private boolean handleRollback(Map<String, Object> params) {
        String targetVersion = (String) params.get("targetVersion");
        if (StringUtils.isEmpty(targetVersion)) {
            throw new MaginaException("目标版本不能为空");
        }
        
        AppVersion targetVersionEntity = appVersionDomainService.getByVersionName(targetVersion);
        if (targetVersionEntity == null) {
            throw new MaginaException("目标版本不存在");
        }
        
        // 设置管理员强制更新标志
        return appVersionDomainService.lambdaUpdate()
                .set(AppVersion::getAdminForce, true)
                .eq(AppVersion::getId, targetVersionEntity.getId())
                .update();
    }
    
    /**
     * 暂停所有更新
     * @return 是否成功
     */
    private boolean handlePauseUpdates() {
        return appVersionDomainService.pauseAllUpdates();
    }
    
    /**
     * 恢复更新推送
     * @return 是否成功
     */
    private boolean handleResumeUpdates() {
        return appVersionDomainService.resumeAllUpdates();
    }
    
    /**
     * 验证版本唯一性（加强版，支持并发安全）
     * @param versionName 版本名称
     * @param versionCode 版本号
     * @param excludeId 排除的ID（更新时使用）
     */
    private synchronized void validateVersionUnique(String versionName, Integer versionCode, Long excludeId) {
        // 使用 synchronized 确保并发安全
        if (StringUtils.isNotEmpty(versionName)) {
            boolean exists = appVersionDomainService.existsByVersionName(versionName, excludeId);
            if (exists) {
                throw new MaginaException("版本名称 '" + versionName + "' 已存在，请使用其他版本名称");
            }
        }

        if (versionCode != null) {
            boolean exists = appVersionDomainService.existsByVersionCode(versionCode, excludeId);
            if (exists) {
                throw new MaginaException("版本号 " + versionCode + " 已存在，请使用其他版本号");
            }
        }
    }
    

    

    

    
    /**
     * 转换为VO
     * @param entity 实体
     * @return VO
     */
    private AppVersionVo convertToVo(AppVersion entity) {
        AppVersionVo vo = new AppVersionVo();
        BeanUtils.copyProperties(entity, vo);
        return vo;
    }
    

}
