package com.hightop.benyin.customer.infrastructure.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.hightop.benyin.customer.infrastructure.enums.*;
import com.hightop.benyin.order.infrastructure.enmu.PayModeEnum;
import com.hightop.benyin.order.infrastructure.entity.TradeOrderInstallment;
import com.hightop.benyin.repair.price.infrastructure.entity.RepairMonthlyPrice;
import com.hightop.benyin.share.infrastructure.type.CosObjectList;
import com.hightop.fario.common.jackson.annotation.JsonAmount;
import com.hightop.magina.standard.code.dictionary.bind.DictItemBind;
import com.hightop.magina.standard.code.dictionary.bind.DictItemEntry;
import com.hightop.magina.standard.ums.user.bind.UserBind;
import com.hightop.magina.standard.ums.user.bind.UserEntry;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 客户-集团实体
 *
 * <AUTHOR>
 * @date 2023-10-24 11:28:14
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@TableName("b_customer_contract")
@ApiModel
public class CustomerContract {
    public static final String CONTRACT_TYPE = "1200";

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @ApiModelProperty("id")
    Long id;

    @TableField(value = "code")
    @ApiModelProperty("编码")
    private String code;

    @TableField(value = "customer_id")
    @ApiModelProperty("客户id")
    @NotNull(message = "客户id不能为空")
    private Long customerId;

    @TableField("contract_name")
    @ApiModelProperty("合约名称")
    @NotBlank(message = "合约名称不能为空")
    String contractName;

    @TableField("contract_type")
    @ApiModelProperty("合同类型(字典项码1200)")
    @DictItemBind(CONTRACT_TYPE)
    @NotNull(message = "合同类型不能为空")
    DictItemEntry contractType;

    @TableField("is_supplement")
    @ApiModelProperty("是否补录")
    Boolean isSupplement=false;

    @TableField("is_renewal")
    @ApiModelProperty("是否续约")
    Boolean isRenewal=false;


    @TableField(value = "sign_time")
    @ApiModelProperty("签定时间")
    @NotNull(message = "签定时间不能为空")
    LocalDateTime signTime;

    @TableField(value = "attachments")
    @ApiModelProperty("附件")
    CosObjectList attachments;

    @TableField("remark")
    @ApiModelProperty("摘要")
    String remark;

    @TableField("total_amount")
    @ApiModelProperty("合同总金额")
    @JsonAmount
    Long totalAmount=0L;

    @TableField("arrears_amount")
    @ApiModelProperty("尾款金额，不分期的时候安装后收取")
    @JsonAmount
    Long arrersAmount=0L;

    @TableField("give_amount")
    @ApiModelProperty("赠品金额")
    @JsonAmount
    Long giveAmount;

    @TableField("prepayment")
    @ApiModelProperty("预付款=首付金额+押金金额")
    @JsonAmount
    Long prepayment;

    @TableField("deposit_amount")
    @ApiModelProperty("定金、押金金额")
    @JsonAmount
    Long depositAmount;

    @TableField("first_amount")
    @ApiModelProperty("购机首付金额")
    @JsonAmount
    Long firstAmount;

    @TableField("agent_id")
    @ApiModelProperty("经办人id")
    Long agentId;

    @TableField("agent_name")
    @ApiModelProperty("经办人姓名")
    String agentName;

    @TableField("sign_id")
    @ApiModelProperty("签约人id")
    Long signId;

    @TableField("sign_name")
    @ApiModelProperty("经办人姓名")
    String signName;

    @TableField("consignee_phone")
    @ApiModelProperty("联系电话")
    String consigneePhone;

    @TableField("consignee")
    @ApiModelProperty("联系人")
    String consignee;

    @TableField("address_id")
    @ApiModelProperty("地址")
    Long addressId;

    @TableField("settle_status")
    @ApiModelProperty("结算状态")
    ContractSettleStatus settleStatus;

    @TableField("merge_type")
    @ApiModelProperty("合并类型")
    MergeTypeEnums mergeType;

    @TableField("status")
    @ApiModelProperty("合同状态")
    ContractStatusEnums status;

    @TableField("is_return")
    @ApiModelProperty("是否退机")
    Boolean isReturn;

    @TableField("pre_pay_status")
    @ApiModelProperty("预付款支付情况")
    Boolean prePayStatus;

    @TableField("arrears_pay_status")
    @ApiModelProperty("尾款支付情况")
    Boolean arrersPayStatus;

    @TableField("return_reason")
    @ApiModelProperty("退机原因")
    String returnReason;

    @TableField("return_time")
    @ApiModelProperty("退机时间")
    LocalDateTime returnTime;

    @TableField("pay_mode")
    @ApiModelProperty("支付方式")
    PayModeEnum payMode;

    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @ApiModelProperty("创建时间")
    LocalDateTime createdAt;

    @TableField(value = "created_by")
    @ApiModelProperty("创建人")
    @UserBind
    private UserEntry createdBy;

    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty("更新时间")
    LocalDateTime updatedAt;
    @TableField("deleted")
    @TableLogic
    @ApiModelProperty("是否删除")
    Integer deleted;

    @TableField("is_install")
    @ApiModelProperty("是否需要安装")
    Boolean isInstall=false;

    @TableField(exist = false)
    @ApiModelProperty("客户名称")
    private String customerName;

    @TableField(exist = false)
    @ApiModelProperty("分店")
    String subbranch;

    @TableField(exist = false)
    @ApiModelProperty("客户编号")
    private String customerSeqId;

    @TableField(exist = false)
    @ApiModelProperty("订单id")
    private Long tradeOrderId;

    @TableField(exist = false)
    @ApiModelProperty("市级区域编码")
    Integer parentRegionCode;

    @TableField(exist = false)
    @ApiModelProperty("省")
    String province;

    @TableField(exist = false)
    @ApiModelProperty("市")
    String city;

    @TableField(exist = false)
    @ApiModelProperty("区")
    String area;

    @TableField(exist = false)
    @ApiModelProperty("-1无需发货0待发货1已发货")
    Integer deliveryStatus;


    @TableField(exist = false)
    @ApiModelProperty("合约到期时间")
    LocalDate expiredDate;

    @TableField(exist = false)
    @ApiModelProperty("合约到期剩余天数")
    Long expiredDay= 0L;


}
